{"name": "DanmakuKit", "version": "1.5.2", "summary": "DanmakuKit is a tool for rapid development of danmaku functions.", "description": "DanmakuKit is a high performance danmaku framework that you can use to customize your own unique danmaku style. It provides an asynchronous and synchronous way for you to draw a danmaku. And it provides three types of danmaku: floating, roof screens and floor screens.", "homepage": "https://github.com/qyz777/DanmakuKit", "license": {"type": "MIT", "file": "LICENSE"}, "authors": {"qyz777": "<EMAIL>"}, "source": {"git": "https://github.com/qyz777/DanmakuKit.git", "tag": "1.5.2"}, "platforms": {"ios": "10.0"}, "swift_versions": ["5.0"], "default_subspecs": ["Core"], "subspecs": [{"name": "Core", "source_files": "Sources/DanmakuKit/Classes/Core/**/*"}, {"name": "Gif", "source_files": "Sources/DanmakuKit/Classes/Gif/**/*", "dependencies": {"DanmakuKit/Core": []}}, {"name": "SwiftUI", "source_files": "Sources/DanmakuKit/Classes/SwiftUI/**/*", "dependencies": {"DanmakuKit/Core": []}}], "swift_version": "5.0"}